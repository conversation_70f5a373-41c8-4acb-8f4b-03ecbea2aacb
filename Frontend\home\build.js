const fs = require("fs")
const path = require("path")
const { minify } = require("html-minifier")
const csso = require("csso")
const terser = require("terser")

// Define paths
const srcDir = path.join(__dirname, "assets")
const distDir = path.join(__dirname, "dist")
const htmlFile = path.join(__dirname, "index.html")

// Ensure dist directory exists
if (fs.existsSync(distDir)) {
  fs.rmdirSync(distDir, { recursive: true })
}
fs.mkdirSync(distDir, { recursive: true })
// Compress index.html
const htmlContent = fs.readFileSync(htmlFile, "utf-8")
const compressedHtml = minify(htmlContent, {
  collapseWhitespace: true,
  removeComments: true,
  minifyCSS: true,
  minifyJS: true,
})
fs.writeFileSync(path.join(distDir, "index.html"), compressedHtml, "utf-8")
console.log("Compressed: index.html")

// Function to process files in assets
function processAssets(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true })
  }

  fs.readdirSync(src).forEach(async (item) => {
    const srcPath = path.join(src, item)
    const destPath = path.join(dest, item)

    if (fs.lstatSync(srcPath).isDirectory()) {
      processAssets(srcPath, destPath)
    } else if (srcPath.endsWith(".css")) {
      const cssContent = fs.readFileSync(srcPath, "utf-8")
      const compressedCss = csso.minify(cssContent).css
      fs.writeFileSync(destPath, compressedCss, "utf-8")
      console.log(`Compressed: ${srcPath}`)
    } else if (srcPath.endsWith(".js")) {
      const jsContent = fs.readFileSync(srcPath, "utf-8")
      const result = await terser.minify(jsContent)
      fs.writeFileSync(destPath, result.code, "utf-8")
      console.log(`Compressed: ${srcPath}`)
    } else {
      fs.copyFileSync(srcPath, destPath)
      console.log(`Moved: ${srcPath} -> ${destPath}`)
    }
  })
}

// Process assets directory
processAssets(srcDir, path.join(distDir, "assets"))
