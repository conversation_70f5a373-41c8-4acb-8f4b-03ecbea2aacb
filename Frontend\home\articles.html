<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>文章资讯 - 法宝</title>
    <meta
      name="description"
      content="法宝文章资讯，分享电商运营、数字商品、自动发货等相关知识和经验"
    />
    <meta name="keywords" content="法宝,文章,资讯,电商运营,数字商品,自动发货" />
    <!-- Favicons -->
    <link rel="shortcut icon" href="assets/favicon.ico" type="image/x-icon" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Vendor CSS Files -->
    <link
      href="assets/vendor/bootstrap/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="assets/vendor/bootstrap-icons/bootstrap-icons.css"
      rel="stylesheet"
    />
    <link href="assets/vendor/aos/aos.css" rel="stylesheet" />

    <!-- Main CSS File -->
    <link href="assets/css/main.css" rel="stylesheet" />
    <link href="assets/css/articles.css" rel="stylesheet" />
  </head>

  <body class="articles-page">
    <header id="header" class="header d-flex align-items-center fixed-top">
      <div
        class="container position-relative d-flex align-items-center justify-content-between"
      >
        <a
          href="index.html"
          class="logo d-flex align-items-center me-auto me-xl-0"
        >
          <svg viewBox="0 0 67.7 18.22">
            <g fill="#fff">
              <path
                d="M19.33 8.65c1.02-.91 1.67-2.29 1.64-3.83-.05-2.67-2.19-4.77-4.7-4.77h-5.78v1.64c0 .7.36 1.37.96 1.66.22.11.44.25.63.4.89.71 1.47 1.83 1.47 3.11 0 .49-.09.96-.25 1.39-.22.6-.57 1.13-1.02 1.54-.23.21-.48.39-.75.53 2.34.75 4.05 3.05 4.05 5.79v.2h8.03c.06-.36.09-.73.09-1.11 0-3.03-1.83-5.6-4.37-6.56M0 16.32h4.23v-.21c0-2.09 1-3.94 2.52-5.02.47-.33.98-.59 1.53-.77v-.79h-1c-.2-.23-.38-.48-.53-.76-.3-.57-.48-1.22-.48-1.92 0-1.54.85-2.87 2.08-3.5.62-.31.98-1 .98-1.73V.04H0v16.28zM0 17.04v1.18h4.58c-.13-.38-.23-.77-.29-1.18H0zM15.51 17.04c-.06.41-.16.81-.29 1.18h7.82c.17-.38.31-.77.42-1.18h-7.95z"
              />
            </g>
            <g fill="#fff">
              <path
                d="M29.08.92h4v2.24h-4zM65.45 1.61h-5.84V.16h-2.24v1.45h-5.82v-.45h-2.24v4.32h2.24V3.85h13.9v1.63h2.25V1.61h-2.25zM65.27 15.31h-5.66v-2.63h7.58v-1.84h-7.58V8.3h7.88V6.46h-18V8.3h7.88v2.54h-7.58v1.84h7.58v2.63h-7.88v2.24h18.03V14h-2.25v1.31zM29.08 7.58h4v2.24h-4zM29.08 15.03h4v2.24h-4zM44.14 11.88l.76 3.43h-7.13l1.3-5.89h8.41V7.17H42V3.91h5.24V1.66H42V0h-2.25v1.66H34.5v2.25h5.25v3.26h-5.48v2.25h2.46l-1.8 8.13H47.74l-1.26-5.67h-2.34z"
              />
            </g>
          </svg>
        </a>

        <nav id="navmenu" class="navmenu">
          <ul>
            <li><a href="index.html">首页</a></li>
            <li><a href="products.html">产品生态</a></li>
            <li><a href="help.html">使用帮助</a></li>
            <li><a href="articles.html" class="active">文章资讯</a></li>
            <li><a href="index.html#scene">系统对接</a></li>
            <li><a href="index.html#introduction">商务合作</a></li>
            <li><a href="index.html#faq">问题答疑</a></li>
          </ul>
          <button
            class="mobile-nav-toggle d-xl-none"
            aria-label="Toggle navigation"
          >
            <span></span>
            <span></span>
            <span></span>
          </button>
        </nav>

        <div class="header-social-links">
          <a class="btn-login" href="https://console.fabao.com">登录控制台</a>
        </div>
      </div>
    </header>

    <main class="main">
      <!-- Hero Carousel -->
      <section id="articles-carousel" class="articles-carousel section">
        <div class="container">
          <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel">
            <div class="carousel-indicators">
              <button
                type="button"
                data-bs-target="#heroCarousel"
                data-bs-slide-to="0"
                class="active"
                aria-current="true"
                aria-label="Slide 1"
              ></button>
              <button
                type="button"
                data-bs-target="#heroCarousel"
                data-bs-slide-to="1"
                aria-label="Slide 2"
              ></button>
              <button
                type="button"
                data-bs-target="#heroCarousel"
                data-bs-slide-to="2"
                aria-label="Slide 3"
              ></button>
            </div>
            <div class="carousel-inner">
              <div class="carousel-item active">
                <div class="carousel-content">
                  <div class="row align-items-center">
                    <div class="col-lg-6">
                      <div class="carousel-text">
                        <h2>数字商品运营新趋势</h2>
                        <p>探索电商数字化转型的最新动态，掌握行业发展脉搏</p>
                        <a
                          href="article-detail.html?id=1"
                          class="btn btn-primary"
                          >立即阅读</a
                        >
                      </div>
                    </div>
                    <div class="col-lg-6">
                      <div class="carousel-image">
                        <img
                          src="https://via.placeholder.com/600x400"
                          alt="数字商品运营"
                          class="img-fluid"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="carousel-item">
                <div class="carousel-content">
                  <div class="row align-items-center">
                    <div class="col-lg-6">
                      <div class="carousel-text">
                        <h2>电商平台对接指南</h2>
                        <p>全面解析主流电商平台接入流程，助力商家快速上线</p>
                        <a
                          href="article-detail.html?id=2"
                          class="btn btn-primary"
                          >立即阅读</a
                        >
                      </div>
                    </div>
                    <div class="col-lg-6">
                      <div class="carousel-image">
                        <img
                          src="https://via.placeholder.com/600x400"
                          alt="平台对接"
                          class="img-fluid"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="carousel-item">
                <div class="carousel-content">
                  <div class="row align-items-center">
                    <div class="col-lg-6">
                      <div class="carousel-text">
                        <h2>自动发货系统优化</h2>
                        <p>提升发货效率，降低运营成本的实用技巧分享</p>
                        <a
                          href="article-detail.html?id=3"
                          class="btn btn-primary"
                          >立即阅读</a
                        >
                      </div>
                    </div>
                    <div class="col-lg-6">
                      <div class="carousel-image">
                        <img
                          src="https://via.placeholder.com/600x400"
                          alt="自动发货"
                          class="img-fluid"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <button
              class="carousel-control-prev"
              type="button"
              data-bs-target="#heroCarousel"
              data-bs-slide="prev"
            >
              <span
                class="carousel-control-prev-icon"
                aria-hidden="true"
              ></span>
              <span class="visually-hidden">Previous</span>
            </button>
            <button
              class="carousel-control-next"
              type="button"
              data-bs-target="#heroCarousel"
              data-bs-slide="next"
            >
              <span
                class="carousel-control-next-icon"
                aria-hidden="true"
              ></span>
              <span class="visually-hidden">Next</span>
            </button>
          </div>
        </div>
      </section>

      <!-- Articles List Section -->
      <section class="articles-list-section">
        <div class="container">
          <!-- Articles List -->
          <div class="articles-list" id="articlesGrid" data-aos="fade-up">
            <!-- 文章列表将在这里显示 -->
          </div>

          <!-- Pagination -->
          <nav
            class="articles-pagination"
            data-aos="fade-up"
            aria-label="文章分页导航"
          >
            <ul
              class="pagination justify-content-center"
              id="paginationContainer"
            >
              <!-- 分页内容将通过JavaScript动态生成 -->
            </ul>
          </nav>

          <!-- Articles Info -->
          <div class="articles-info text-center" data-aos="fade-up">
            <p class="text-muted">
              共 <span id="totalArticles">0</span> 篇文章，当前第
              <span id="currentPageInfo">1</span> 页，共
              <span id="totalPages">1</span> 页
            </p>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer id="footer" class="footer dark-background">
      <div class="container">
        <div class="row gy-3">
          <div class="col-lg-3 col-md-6 d-flex">
            <i class="bi bi-geo-alt icon"></i>
            <div class="address">
              <h4>地址</h4>
              <p>中国 · 深圳</p>
            </div>
          </div>

          <div class="col-lg-3 col-md-6 d-flex">
            <i class="bi bi-telephone icon"></i>
            <div class="address">
              <h4>联系方式</h4>
              <p>
                <strong>邮箱:</strong> <span><EMAIL></span><br />
                <strong>QQ群:</strong> <span>123456789</span>
              </p>
            </div>
          </div>

          <div class="col-lg-3 col-md-6 d-flex">
            <i class="bi bi-clock icon"></i>
            <div class="address">
              <h4>服务时间</h4>
              <p>
                <strong>周一至周五:</strong> <span>9:00 - 18:00</span><br />
                <strong>周末:</strong> <span>在线支持</span>
              </p>
            </div>
          </div>

          <div class="col-lg-3 col-md-6">
            <h4>关注我们</h4>
            <div class="social-links d-flex">
              <a href="#" class="twitter"><i class="bi bi-twitter-x"></i></a>
              <a href="#" class="facebook"><i class="bi bi-facebook"></i></a>
              <a href="#" class="instagram"><i class="bi bi-instagram"></i></a>
              <a href="#" class="linkedin"><i class="bi bi-linkedin"></i></a>
            </div>
          </div>
        </div>
      </div>

      <div class="container copyright text-center mt-4">
        <p>
          © <span>Copyright</span> <strong class="px-1 sitename">法宝</strong>
          <span>All Rights Reserved</span>
        </p>
      </div>
    </footer>

    <!-- Scroll Top -->
    <a
      href="#"
      id="scroll-top"
      class="scroll-top d-flex align-items-center justify-content-center"
      ><i class="bi bi-arrow-up-short"></i
    ></a>

    <!-- Vendor JS Files -->
    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="assets/vendor/aos/aos.js"></script>

    <!-- Main JS File -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/articles.js"></script>
  </body>
</html>
